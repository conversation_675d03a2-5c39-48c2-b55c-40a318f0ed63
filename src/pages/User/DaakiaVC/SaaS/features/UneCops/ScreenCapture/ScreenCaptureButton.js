/*eslint-disable*/
import React, { useState, useEffect } from 'react';
import moment from 'moment';
import html2canvas from 'html2canvas';
import { DataReceivedEvent } from '../../../../utils/constants';
import { ReactComponent as CaptureIcon } from '../../../../assets/icons/Copy.svg';
import { SaasService } from '../../../services/saasServices';
import { useSaasHelpers } from '../../../helpers/helpers';
import './ScreenCaptureButton.scss';

export function ScreenCaptureButton({
  room,
  screenShareTracks,
  // focusTrack,
  setToastNotification,
  setToastStatus,
  setShowToast,
  setShowPopover,
  meetingDetails
}) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [shouldDownload, setShouldDownload] = useState(true); // State to control download
  const { saasHostToken } = useSaasHelpers();

  // Automatically determine capture mode based on screen sharing status
  const captureMode = (screenShareTracks && screenShareTracks.length > 0) ? 'screenshare' : 'gridlayout';

  const uploadScreenshot = async (formData) => {
    try {
      const response = await SaasService.uploadScreenshot(
        formData,
        { "Content-Type": "multipart/form-data" },
        saasHostToken
      );
      if (response.success === 0) {
        throw new Error("Failed to upload screenshot");
      }
      return response;
    } catch (error) {
      console.error("Screenshot upload error:", error);
      throw new Error(`Screenshot upload failed: ${error.message}`);
    }
  };



  const captureFocusedScreenShare = async () => {
    try {
      const screenShareVideo = document.querySelector('video[data-lk-source="screen_share"]');

      if (!screenShareVideo) {
        throw new Error("Screen share video not found - make sure screen sharing is active");
      }

      if (screenShareVideo.videoWidth === 0 || screenShareVideo.videoHeight === 0) {
        throw new Error("Screen share video has no dimensions - video may not be loaded yet");
      }



      // Create canvas to capture the screen share video AS IT APPEARS (with blur if blurred)
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      // Set canvas dimensions to match video's actual dimensions
      canvas.width = screenShareVideo.videoWidth;
      canvas.height = screenShareVideo.videoHeight;

      // Draw the current video frame to canvas exactly as it appears
      ctx.drawImage(screenShareVideo, 0, 0, canvas.width, canvas.height);

      // Convert canvas to blob and process
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error("Failed to create blob from screen share video canvas"));
            return;
          }

          const formData = new FormData();
          const eventName = meetingDetails?.event_name?.replace(/[^a-zA-Z0-9]/g, '-') || 'screenshot';
          const filename = `${eventName}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
          const file = new File([blob], filename, { type: 'image/png' });
          formData.append('image', file);
          if (room?.roomInfo?.name) {
            formData.append('meeting_uid', room.roomInfo.name);
          }

          // Upload to API
          uploadScreenshot(formData);

          // Download locally only if shouldDownload is true
          if (shouldDownload) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }

          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Focused screen share capture error:", error);
      throw new Error(`Focused capture failed: ${error.message}`);
    }
  };

  const captureGridLayout = async () => {
    try {
      // Determine which layout element to capture based on what's currently active
      let layoutElement = null;
      let layoutName = '';

      // Check if drawer is open (has 'show' class)
      const drawerOpen = document.querySelector('.sd-container.show');

      // Check for whiteboard focus layout first
      const whiteboardFocus = document.querySelector('.lk-focus-layout-wrapper.whiteboard-focus');
      if (whiteboardFocus) {
        // If drawer is open, capture the entire wrapper, otherwise just the layout
        layoutElement = drawerOpen ? whiteboardFocus : whiteboardFocus;
        layoutName = 'Whiteboard Focus Layout';
      } else {
        // Check for regular focus layout
        const focusLayoutWrapper = document.querySelector('.lk-focus-layout-wrapper:not(.whiteboard-focus)');
        const focusLayout = document.querySelector('.lk-focus-layout');
        if (focusLayoutWrapper || focusLayout) {
          // If drawer is open, capture the entire wrapper, otherwise just the focus layout
          layoutElement = drawerOpen && focusLayoutWrapper ? focusLayoutWrapper : focusLayout;
          layoutName = 'Focus Layout';
        } else {
          // Default to grid layout
          const gridLayoutWrapper = document.querySelector('.lk-grid-layout-wrapper');
          const gridLayout = document.querySelector('.lk-grid-layout');
          if (gridLayoutWrapper || gridLayout) {
            // If drawer is open, capture the entire wrapper, otherwise just the grid layout
            layoutElement = drawerOpen && gridLayoutWrapper ? gridLayoutWrapper : gridLayout;
            layoutName = 'Grid Layout';
          }
        }
      }

      if (!layoutElement) {
        throw new Error("No active layout found - make sure you're in the video conference view");
      }

      // Use html2canvas to capture the active layout element
      const canvas = await html2canvas(layoutElement, {
        useCORS: true,
        allowTaint: true,
        scale: 1,
        backgroundColor: '#000000',
        logging: false,
        width: layoutElement.offsetWidth,
        height: layoutElement.offsetHeight
      });



      // Convert canvas to blob and process
      return new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (!blob) {
            reject(new Error(`Failed to create blob from ${layoutName} canvas`));
            return;
          }

          const formData = new FormData();
          const eventName = meetingDetails?.event_name?.replace(/[^a-zA-Z0-9]/g, '-') || 'screenshot';
          const filename = `${eventName}-${moment().format('YYYY-MM-DD-HH-mm-ss')}.png`;
          const file = new File([blob], filename, { type: 'image/png' });
          formData.append('image', file);
          if (room?.roomInfo?.name) {
            formData.append('meeting_uid', room.roomInfo.name);
          }
          // Upload to API
          uploadScreenshot(formData);

          // Download locally only if shouldDownload is true
          if (shouldDownload) {
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }

          resolve();
        }, 'image/png', 0.95);
      });

    } catch (error) {
      console.error("Grid layout capture error:", error);
      throw new Error(`Grid layout capture failed: ${error.message}`);
    }
  };

  const broadcastCaptureEvent = async () => {
    if (!room || !room.localParticipant) return;

    const encoder = new TextEncoder();
    const data = encoder.encode(
      JSON.stringify({
        action: DataReceivedEvent.SCREEN_CAPTURE_TAKEN,
        participantName: room.localParticipant.name || room.localParticipant.identity,
        timestamp: Date.now(),
      })
    );

    await room.localParticipant.publishData(data, {
      reliable: true,
    });
  };

  const handleScreenCapture = async () => {
    if (isCapturing) return;

    setIsCapturing(true);

    try {
      if (captureMode === 'screenshare') {
        // Check if screen sharing is active for screenshare mode
        if (!screenShareTracks || screenShareTracks.length === 0) {
          setToastNotification("No active screen share to capture");
          setToastStatus("warning");
          setShowToast(true);
          setIsCapturing(false);
          return;
        }
        // Capture the focused screen share from focus layout
        await captureFocusedScreenShare();
      } else if (captureMode === 'gridlayout') {
        // Capture the grid layout
        await captureGridLayout();
      }

      // Broadcast to all participants
      await broadcastCaptureEvent();

      // Show success toast
      setToastNotification("Screen capture successful");
      setToastStatus("success");
      setShowToast(true);

      // Close the popover
      if (setShowPopover) {
        setShowPopover(false);
      }

    } catch (error) {
      console.error("Screen capture failed:", error);
      setToastNotification(`Screen capture failed: ${error.message}`);
      setToastStatus("error");
      setShowToast(true);
    } finally {
      setIsCapturing(false);
    }
  };

  useEffect(() => {

    const handleKeyDown = (event) => {
      if (event.shiftKey && event.key.toLowerCase() === 's') {
        // Allow capture in both modes - no need to check for screen share tracks in grid mode
        if (!isCapturing) {
          event.preventDefault();
          handleScreenCapture();
        }
      }
    };

    const handleKeyUp = () => {
      // No need for key tracking with simple Shift + S
    };

    // Add event listeners
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // Cleanup event listeners on unmount
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isCapturing, captureMode]);

  return (
    <div
      onClick={handleScreenCapture}
      className={`screen-capture-button ${isCapturing ? 'capturing' : ''}`}
      title={`Capture ${captureMode === 'screenshare' ? 'Screen Share' : 'Grid Layout'} (Shift+S)`}
    >
      <CaptureIcon
        style={{ color: isCapturing ? '#1890ff' : '#fff' }}
      />
    </div>
  );
}
